package com.example.springvueapp.k8s.exception;

/**
 * Kubernetes 操作异常类
 * 用于处理 K8s 相关操作中的异常情况
 */
public class K8sException extends RuntimeException {

    /**
     * 错误代码
     */
    private final String code;

    /**
     * 操作类型
     */
    private final String operation;

    /**
     * 资源信息
     */
    private final String resource;

    public K8sException(String message) {
        super(message);
        this.code = "K8S_ERROR";
        this.operation = null;
        this.resource = null;
    }

    public K8sException(String code, String message) {
        super(message);
        this.code = code;
        this.operation = null;
        this.resource = null;
    }

    public K8sException(String message, Throwable cause) {
        super(message, cause);
        this.code = "K8S_ERROR";
        this.operation = null;
        this.resource = null;
    }

    public K8sException(String code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.operation = null;
        this.resource = null;
    }

    public K8sException(String code, String operation, String resource, String message) {
        super(message);
        this.code = code;
        this.operation = operation;
        this.resource = resource;
    }

    public K8sException(String code, String operation, String resource, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.operation = operation;
        this.resource = resource;
    }

    // 静态工厂方法
    public static K8sException clientError(String message) {
        return new K8sException("K8S_CLIENT_ERROR", message);
    }

    public static K8sException clientError(String message, Throwable cause) {
        return new K8sException("K8S_CLIENT_ERROR", message, cause);
    }

    public static K8sException configError(String message) {
        return new K8sException("K8S_CONFIG_ERROR", message);
    }

    public static K8sException configError(String message, Throwable cause) {
        return new K8sException("K8S_CONFIG_ERROR", message, cause);
    }

    public static K8sException yamlError(String message) {
        return new K8sException("K8S_YAML_ERROR", message);
    }

    public static K8sException yamlError(String message, Throwable cause) {
        return new K8sException("K8S_YAML_ERROR", message, cause);
    }

    public static K8sException operationError(String operation, String resource, String message) {
        return new K8sException("K8S_OPERATION_ERROR", operation, resource, message);
    }

    public static K8sException operationError(String operation, String resource, String message, Throwable cause) {
        return new K8sException("K8S_OPERATION_ERROR", operation, resource, message, cause);
    }

    public static K8sException connectionError(String message) {
        return new K8sException("K8S_CONNECTION_ERROR", message);
    }

    public static K8sException connectionError(String message, Throwable cause) {
        return new K8sException("K8S_CONNECTION_ERROR", message, cause);
    }

    public static K8sException authenticationError(String message) {
        return new K8sException("K8S_AUTH_ERROR", message);
    }

    public static K8sException authenticationError(String message, Throwable cause) {
        return new K8sException("K8S_AUTH_ERROR", message, cause);
    }

    public static K8sException permissionError(String message) {
        return new K8sException("K8S_PERMISSION_ERROR", message);
    }

    public static K8sException permissionError(String message, Throwable cause) {
        return new K8sException("K8S_PERMISSION_ERROR", message, cause);
    }

    // Getter 方法
    public String getCode() {
        return code;
    }

    public String getOperation() {
        return operation;
    }

    public String getResource() {
        return resource;
    }

    /**
     * 获取详细的错误信息
     */
    public String getDetailedMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append("K8s操作异常");
        
        if (operation != null) {
            sb.append(" [操作: ").append(operation).append("]");
        }
        
        if (resource != null) {
            sb.append(" [资源: ").append(resource).append("]");
        }
        
        sb.append(" - ").append(getMessage());
        
        if (getCause() != null) {
            sb.append(" (原因: ").append(getCause().getMessage()).append(")");
        }
        
        return sb.toString();
    }

    @Override
    public String toString() {
        return "K8sException{" +
                "code='" + code + '\'' +
                ", operation='" + operation + '\'' +
                ", resource='" + resource + '\'' +
                ", message='" + getMessage() + '\'' +
                '}';
    }
}
