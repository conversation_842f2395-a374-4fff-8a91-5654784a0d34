package com.example.springvueapp.k8s.client;

import com.example.springvueapp.cloudplatform.model.CloudPlatformDTO;
import com.example.springvueapp.cloudplatform.service.CloudPlatformService;
import com.example.springvueapp.k8s.exception.K8sException;
import com.example.springvueapp.k8s.model.CloudPlatformConfig;
import io.fabric8.kubernetes.client.Config;
import io.fabric8.kubernetes.client.ConfigBuilder;
import io.fabric8.kubernetes.client.DefaultKubernetesClient;
import io.fabric8.kubernetes.client.KubernetesClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Kubernetes 客户端管理器
 * 负责创建、缓存和管理多个 K8s 集群的客户端连接
 * 基于 CloudPlatformDTO 配置来管理客户端实例
 */
@Component
public class K8sClientManager {

    private static final Logger logger = LoggerFactory.getLogger(K8sClientManager.class);

    /**
     * 客户端缓存，使用配置键作为缓存键
     */
    private final ConcurrentMap<String, KubernetesClient> clientCache = new ConcurrentHashMap<>();

    /**
     * 云平台服务，用于获取平台配置
     */
    private final CloudPlatformService cloudPlatformService;

    @Autowired
    public K8sClientManager(CloudPlatformService cloudPlatformService) {
        this.cloudPlatformService = cloudPlatformService;
    }

    /**
     * 根据云平台配置获取 Kubernetes 客户端
     * 如果客户端已存在则返回缓存的实例，否则创建新的客户端
     *
     * @param config 云平台配置
     * @return Kubernetes 客户端
     */
    public Mono<KubernetesClient> getClient(CloudPlatformConfig config) {
        if (config == null) {
            return Mono.error(K8sException.configError("云平台配置不能为空"));
        }

        // 如果提供了平台ID，从数据库加载完整配置
        if (config.getId() != null) {
            return loadConfigFromDatabase(config)
                    .flatMap(this::createOrGetCachedClient);
        } else {
            // 直接使用提供的配置
            return createOrGetCachedClient(config);
        }
    }

    /**
     * 根据云平台DTO获取 Kubernetes 客户端
     *
     * @param platformDTO 云平台DTO
     * @return Kubernetes 客户端
     */
    public Mono<KubernetesClient> getClient(CloudPlatformDTO platformDTO) {
        if (platformDTO == null) {
            return Mono.error(K8sException.configError("云平台配置不能为空"));
        }

        CloudPlatformConfig config = convertToConfig(platformDTO);
        return createOrGetCachedClient(config);
    }

    /**
     * 创建或获取缓存的客户端
     *
     * @param config 云平台配置
     * @return Kubernetes 客户端
     */
    private Mono<KubernetesClient> createOrGetCachedClient(CloudPlatformConfig config) {
        return Mono.fromCallable(() -> {
            String configKey = config.getConfigKey();
            
            // 检查缓存中是否已存在客户端
            KubernetesClient existingClient = clientCache.get(configKey);
            if (existingClient != null && !existingClient.isAdaptable(KubernetesClient.class)) {
                // 客户端已失效，从缓存中移除
                clientCache.remove(configKey);
                existingClient = null;
            }

            if (existingClient != null) {
                logger.debug("返回缓存的K8s客户端: {}", configKey);
                return existingClient;
            }

            // 创建新的客户端
            logger.info("创建新的K8s客户端: {}", configKey);
            KubernetesClient newClient = createKubernetesClient(config);
            
            // 缓存客户端
            clientCache.put(configKey, newClient);
            
            return newClient;
        })
        .onErrorMap(throwable -> {
            if (throwable instanceof K8sException) {
                return throwable;
            }
            return K8sException.clientError("创建K8s客户端失败: " + throwable.getMessage(), throwable);
        });
    }

    /**
     * 从数据库加载云平台配置
     *
     * @param config 包含ID和用户ID的配置
     * @return 完整的云平台配置
     */
    private Mono<CloudPlatformConfig> loadConfigFromDatabase(CloudPlatformConfig config) {
        return cloudPlatformService.findCloudPlatformById(config.getUserId(), config.getId())
                .map(this::convertToConfig)
                .doOnNext(loadedConfig -> logger.debug("从数据库加载云平台配置: {}", loadedConfig))
                .onErrorMap(throwable -> K8sException.configError("加载云平台配置失败: " + throwable.getMessage(), throwable));
    }

    /**
     * 将 CloudPlatformDTO 转换为 CloudPlatformConfig
     *
     * @param dto 云平台DTO
     * @return 云平台配置
     */
    private CloudPlatformConfig convertToConfig(CloudPlatformDTO dto) {
        CloudPlatformConfig config = new CloudPlatformConfig();
        config.setId(dto.getId());
        config.setName(dto.getName());
        config.setType(dto.getType());
        config.setUrl(dto.getUrl());
        config.setSecret(dto.getSecret());
        config.setRegion(dto.getRegion());
        config.setVersion(dto.getVersion());
        config.setTags(dto.getTags());
        // 注意：这里需要从认证上下文获取用户ID，暂时设为null
        config.setUserId(null);
        return config;
    }

    /**
     * 创建 Kubernetes 客户端
     *
     * @param config 云平台配置
     * @return Kubernetes 客户端
     */
    private KubernetesClient createKubernetesClient(CloudPlatformConfig config) {
        try {
            // 验证配置
            validateConfig(config);

            // 构建 Kubernetes 配置
            Config k8sConfig = buildKubernetesConfig(config);

            // 创建客户端
            return new DefaultKubernetesClient(k8sConfig);

        } catch (Exception e) {
            throw K8sException.clientError("创建Kubernetes客户端失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证云平台配置
     *
     * @param config 云平台配置
     */
    private void validateConfig(CloudPlatformConfig config) {
        if (config.getUrl() == null || config.getUrl().trim().isEmpty()) {
            throw K8sException.configError("Kubernetes集群URL不能为空");
        }

        if (!"KUBERNETES".equalsIgnoreCase(config.getType()) && !"K3S".equalsIgnoreCase(config.getType())) {
            throw K8sException.configError("不支持的平台类型: " + config.getType());
        }
    }

    /**
     * 构建 Kubernetes 配置
     *
     * @param config 云平台配置
     * @return Kubernetes 配置
     */
    private Config buildKubernetesConfig(CloudPlatformConfig config) {
        ConfigBuilder configBuilder = new ConfigBuilder()
                .withMasterUrl(config.getUrl())
                .withTrustCerts(true); // 在生产环境中应该根据需要配置

        // 如果提供了认证信息，配置认证
        if (config.getSecret() != null && !config.getSecret().trim().isEmpty()) {
            // 这里可以根据secret的格式来解析不同类型的认证信息
            // 例如：token、kubeconfig内容等
            configBuilder.withOauthToken(config.getSecret());
        }

        return configBuilder.build();
    }

    /**
     * 移除指定配置的客户端缓存
     *
     * @param config 云平台配置
     */
    public void removeClient(CloudPlatformConfig config) {
        if (config != null) {
            String configKey = config.getConfigKey();
            KubernetesClient client = clientCache.remove(configKey);
            if (client != null) {
                try {
                    client.close();
                    logger.info("已移除并关闭K8s客户端: {}", configKey);
                } catch (Exception e) {
                    logger.warn("关闭K8s客户端时发生错误: {}", configKey, e);
                }
            }
        }
    }

    /**
     * 清理所有客户端缓存
     */
    public void clearAllClients() {
        logger.info("清理所有K8s客户端缓存，当前缓存数量: {}", clientCache.size());
        
        clientCache.forEach((key, client) -> {
            try {
                client.close();
                logger.debug("已关闭K8s客户端: {}", key);
            } catch (Exception e) {
                logger.warn("关闭K8s客户端时发生错误: {}", key, e);
            }
        });
        
        clientCache.clear();
        logger.info("所有K8s客户端缓存已清理完成");
    }

    /**
     * 获取当前缓存的客户端数量
     *
     * @return 缓存的客户端数量
     */
    public int getCachedClientCount() {
        return clientCache.size();
    }

    /**
     * 检查指定配置的客户端是否已缓存
     *
     * @param config 云平台配置
     * @return 是否已缓存
     */
    public boolean isClientCached(CloudPlatformConfig config) {
        return config != null && clientCache.containsKey(config.getConfigKey());
    }
}
