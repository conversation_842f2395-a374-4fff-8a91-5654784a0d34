package com.example.springvueapp.k8s.model;

import com.fasterxml.jackson.annotation.JsonInclude;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Map;

/**
 * Kubernetes Apply 操作请求模型
 * 用于接收前端发送的 YAML 应用请求
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class K8sApplyRequest {

    /**
     * 云平台配置信息
     */
    @NotNull(message = "云平台配置不能为空")
    private CloudPlatformConfig cloudPlatform;

    /**
     * YAML 内容（支持多资源文档）
     */
    @NotBlank(message = "YAML内容不能为空")
    private String yamlContent;

    /**
     * 目标命名空间（可选，如果YAML中未指定）
     */
    private String namespace;

    /**
     * 是否强制应用（覆盖现有资源）
     */
    private Boolean force;

    /**
     * 是否验证YAML格式
     */
    private Boolean validate;

    /**
     * 额外的应用选项
     */
    private Map<String, String> options;

    /**
     * 操作描述
     */
    private String description;

    // 构造函数
    public K8sApplyRequest() {
        this.force = false;
        this.validate = true;
    }

    public K8sApplyRequest(CloudPlatformConfig cloudPlatform, String yamlContent) {
        this();
        this.cloudPlatform = cloudPlatform;
        this.yamlContent = yamlContent;
    }

    public K8sApplyRequest(CloudPlatformConfig cloudPlatform, String yamlContent, 
                          String namespace, Boolean force, Boolean validate) {
        this.cloudPlatform = cloudPlatform;
        this.yamlContent = yamlContent;
        this.namespace = namespace;
        this.force = force != null ? force : false;
        this.validate = validate != null ? validate : true;
    }

    // Getter 和 Setter 方法
    public CloudPlatformConfig getCloudPlatform() {
        return cloudPlatform;
    }

    public void setCloudPlatform(CloudPlatformConfig cloudPlatform) {
        this.cloudPlatform = cloudPlatform;
    }

    public String getYamlContent() {
        return yamlContent;
    }

    public void setYamlContent(String yamlContent) {
        this.yamlContent = yamlContent;
    }

    public String getNamespace() {
        return namespace;
    }

    public void setNamespace(String namespace) {
        this.namespace = namespace;
    }

    public Boolean getForce() {
        return force;
    }

    public void setForce(Boolean force) {
        this.force = force;
    }

    public Boolean getValidate() {
        return validate;
    }

    public void setValidate(Boolean validate) {
        this.validate = validate;
    }

    public Map<String, String> getOptions() {
        return options;
    }

    public void setOptions(Map<String, String> options) {
        this.options = options;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "K8sApplyRequest{" +
                "cloudPlatform=" + cloudPlatform +
                ", yamlContent='" + (yamlContent != null ? yamlContent.substring(0, Math.min(100, yamlContent.length())) + "..." : null) + '\'' +
                ", namespace='" + namespace + '\'' +
                ", force=" + force +
                ", validate=" + validate +
                ", options=" + options +
                ", description='" + description + '\'' +
                '}';
    }
}
