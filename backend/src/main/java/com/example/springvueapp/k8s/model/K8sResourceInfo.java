package com.example.springvueapp.k8s.model;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Map;

/**
 * Kubernetes 资源信息模型
 * 用于描述 K8s 操作中涉及的资源详情
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class K8sResourceInfo {

    /**
     * 资源类型（如：Deployment, Service, ConfigMap等）
     */
    private String kind;

    /**
     * API版本
     */
    private String apiVersion;

    /**
     * 资源名称
     */
    private String name;

    /**
     * 命名空间
     */
    private String namespace;

    /**
     * 资源状态（Created, Updated, Deleted, Failed等）
     */
    private String status;

    /**
     * 操作结果消息
     */
    private String message;

    /**
     * 资源标签
     */
    private Map<String, String> labels;

    /**
     * 资源注解
     */
    private Map<String, String> annotations;

    /**
     * 错误信息（如果操作失败）
     */
    private String error;

    // 构造函数
    public K8sResourceInfo() {
    }

    public K8sResourceInfo(String kind, String name, String namespace, String status) {
        this.kind = kind;
        this.name = name;
        this.namespace = namespace;
        this.status = status;
    }

    public K8sResourceInfo(String kind, String apiVersion, String name, String namespace, 
                          String status, String message) {
        this.kind = kind;
        this.apiVersion = apiVersion;
        this.name = name;
        this.namespace = namespace;
        this.status = status;
        this.message = message;
    }

    // 静态工厂方法
    public static K8sResourceInfo success(String kind, String name, String namespace, String status, String message) {
        return new K8sResourceInfo(kind, null, name, namespace, status, message);
    }

    public static K8sResourceInfo failure(String kind, String name, String namespace, String error) {
        K8sResourceInfo info = new K8sResourceInfo(kind, name, namespace, "Failed");
        info.setError(error);
        return info;
    }

    // Getter 和 Setter 方法
    public String getKind() {
        return kind;
    }

    public void setKind(String kind) {
        this.kind = kind;
    }

    public String getApiVersion() {
        return apiVersion;
    }

    public void setApiVersion(String apiVersion) {
        this.apiVersion = apiVersion;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNamespace() {
        return namespace;
    }

    public void setNamespace(String namespace) {
        this.namespace = namespace;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Map<String, String> getLabels() {
        return labels;
    }

    public void setLabels(Map<String, String> labels) {
        this.labels = labels;
    }

    public Map<String, String> getAnnotations() {
        return annotations;
    }

    public void setAnnotations(Map<String, String> annotations) {
        this.annotations = annotations;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    /**
     * 获取资源的完整标识符
     */
    public String getResourceIdentifier() {
        StringBuilder identifier = new StringBuilder();
        if (kind != null) {
            identifier.append(kind);
        }
        if (name != null) {
            identifier.append("/").append(name);
        }
        if (namespace != null && !namespace.isEmpty()) {
            identifier.append(" (namespace: ").append(namespace).append(")");
        }
        return identifier.toString();
    }

    /**
     * 检查操作是否成功
     */
    public boolean isSuccess() {
        return error == null && !"Failed".equals(status);
    }

    @Override
    public String toString() {
        return "K8sResourceInfo{" +
                "kind='" + kind + '\'' +
                ", apiVersion='" + apiVersion + '\'' +
                ", name='" + name + '\'' +
                ", namespace='" + namespace + '\'' +
                ", status='" + status + '\'' +
                ", message='" + message + '\'' +
                ", error='" + error + '\'' +
                '}';
    }
}
