package com.example.springvueapp.k8s.model;

import com.fasterxml.jackson.annotation.JsonInclude;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Map;

/**
 * 云平台配置模型
 * 用于 K8s 操作中的云平台连接配置
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CloudPlatformConfig {

    /**
     * 云平台ID（可选，如果提供则从数据库加载配置）
     */
    private Long id;

    /**
     * 平台名称
     */
    @NotBlank(message = "平台名称不能为空")
    private String name;

    /**
     * 平台类型
     */
    @NotBlank(message = "平台类型不能为空")
    private String type;

    /**
     * 访问地址
     */
    @NotBlank(message = "访问地址不能为空")
    private String url;

    /**
     * 认证密钥
     */
    private String secret;

    /**
     * 所属区域
     */
    private String region;

    /**
     * 平台版本
     */
    private String version;

    /**
     * 标签信息
     */
    private Map<String, String> tags;

    /**
     * 用户ID（用于权限验证）
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    // 构造函数
    public CloudPlatformConfig() {
    }

    public CloudPlatformConfig(Long id, Long userId) {
        this.id = id;
        this.userId = userId;
    }

    public CloudPlatformConfig(String name, String type, String url, String secret, Long userId) {
        this.name = name;
        this.type = type;
        this.url = url;
        this.secret = secret;
        this.userId = userId;
    }

    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Map<String, String> getTags() {
        return tags;
    }

    public void setTags(Map<String, String> tags) {
        this.tags = tags;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * 生成配置的唯一标识符
     * 用于客户端缓存的键
     */
    public String getConfigKey() {
        if (id != null) {
            return "id:" + id;
        }
        return String.format("config:%s:%s:%s", type, url, userId);
    }

    @Override
    public String toString() {
        return "CloudPlatformConfig{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", type='" + type + '\'' +
                ", url='" + url + '\'' +
                ", region='" + region + '\'' +
                ", version='" + version + '\'' +
                ", tags=" + tags +
                ", userId=" + userId +
                '}';
    }
}
