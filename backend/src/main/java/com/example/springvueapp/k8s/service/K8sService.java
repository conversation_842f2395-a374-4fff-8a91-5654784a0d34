package com.example.springvueapp.k8s.service;

import com.example.springvueapp.cloudplatform.model.CloudPlatformDTO;
import com.example.springvueapp.k8s.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * Kubernetes 服务门面类
 * 提供简化的 K8s 操作 API，供其他模块使用
 * 不包含 Web 层代码，仅作为工具服务
 */
@Service
public class K8sService {

    private static final Logger logger = LoggerFactory.getLogger(K8sService.class);

    private final K8sOperationService operationService;

    @Autowired
    public K8sService(K8sOperationService operationService) {
        this.operationService = operationService;
    }

    /**
     * 应用 YAML 资源到 Kubernetes 集群
     * 简化版本的 apply 方法，使用 CloudPlatformDTO
     *
     * @param platformDTO 云平台配置
     * @param yamlContent YAML 内容
     * @return 操作结果
     */
    public Mono<K8sOperationResult> applyYaml(CloudPlatformDTO platformDTO, String yamlContent) {
        return applyYaml(platformDTO, yamlContent, null, false, true, null);
    }

    /**
     * 应用 YAML 资源到 Kubernetes 集群
     * 完整版本的 apply 方法
     *
     * @param platformDTO 云平台配置
     * @param yamlContent YAML 内容
     * @param namespace 目标命名空间（可选）
     * @param force 是否强制应用
     * @param validate 是否验证 YAML
     * @param description 操作描述
     * @return 操作结果
     */
    public Mono<K8sOperationResult> applyYaml(CloudPlatformDTO platformDTO, String yamlContent, 
                                             String namespace, Boolean force, Boolean validate, String description) {
        logger.info("执行K8s Apply操作: platform={}, namespace={}, force={}", 
            platformDTO.getName(), namespace, force);

        // 构建请求对象
        CloudPlatformConfig config = convertToConfig(platformDTO);
        K8sApplyRequest request = new K8sApplyRequest(config, yamlContent, namespace, force, validate);
        request.setDescription(description);

        return operationService.apply(request);
    }

    /**
     * 从 Kubernetes 集群删除 YAML 资源
     * 简化版本的 delete 方法
     *
     * @param platformDTO 云平台配置
     * @param yamlContent YAML 内容
     * @return 操作结果
     */
    public Mono<K8sOperationResult> deleteYaml(CloudPlatformDTO platformDTO, String yamlContent) {
        return deleteYaml(platformDTO, yamlContent, null, false, "Background", null, null);
    }

    /**
     * 从 Kubernetes 集群删除 YAML 资源
     * 完整版本的 delete 方法
     *
     * @param platformDTO 云平台配置
     * @param yamlContent YAML 内容
     * @param namespace 目标命名空间（可选）
     * @param force 是否强制删除
     * @param deletionPolicy 删除策略
     * @param gracePeriodSeconds 宽限期
     * @param description 操作描述
     * @return 操作结果
     */
    public Mono<K8sOperationResult> deleteYaml(CloudPlatformDTO platformDTO, String yamlContent, 
                                              String namespace, Boolean force, String deletionPolicy, 
                                              Long gracePeriodSeconds, String description) {
        logger.info("执行K8s Delete操作: platform={}, namespace={}, force={}", 
            platformDTO.getName(), namespace, force);

        // 构建请求对象
        CloudPlatformConfig config = convertToConfig(platformDTO);
        K8sDeleteRequest request = new K8sDeleteRequest(config, yamlContent, namespace, force, deletionPolicy);
        request.setGracePeriodSeconds(gracePeriodSeconds);
        request.setDescription(description);

        return operationService.delete(request);
    }

    /**
     * 测试与 Kubernetes 集群的连接
     *
     * @param platformDTO 云平台配置
     * @return 连接是否成功
     */
    public Mono<Boolean> testConnection(CloudPlatformDTO platformDTO) {
        logger.debug("测试K8s集群连接: {}", platformDTO.getName());
        
        CloudPlatformConfig config = convertToConfig(platformDTO);
        return operationService.testConnection(config);
    }

    /**
     * 获取 Kubernetes 集群信息
     *
     * @param platformDTO 云平台配置
     * @return 集群信息
     */
    public Mono<String> getClusterInfo(CloudPlatformDTO platformDTO) {
        logger.debug("获取K8s集群信息: {}", platformDTO.getName());
        
        CloudPlatformConfig config = convertToConfig(platformDTO);
        return operationService.getClusterInfo(config);
    }

    /**
     * 验证 YAML 内容格式
     *
     * @param yamlContent YAML 内容
     * @return 是否为有效格式
     */
    public boolean validateYaml(String yamlContent) {
        // 这里可以注入 K8sYamlParser，但为了简化，直接创建实例
        K8sYamlParser parser = new K8sYamlParser();
        return parser.validateYamlFormat(yamlContent);
    }

    /**
     * 获取 YAML 内容中的资源数量
     *
     * @param yamlContent YAML 内容
     * @return 资源数量
     */
    public int getResourceCount(String yamlContent) {
        K8sYamlParser parser = new K8sYamlParser();
        return parser.getResourceCount(yamlContent);
    }

    /**
     * 获取 YAML 内容中的资源摘要
     *
     * @param yamlContent YAML 内容
     * @return 资源摘要列表
     */
    public java.util.List<String> getResourceSummary(String yamlContent) {
        K8sYamlParser parser = new K8sYamlParser();
        return parser.getResourceSummary(yamlContent);
    }

    /**
     * 将 CloudPlatformDTO 转换为 CloudPlatformConfig
     *
     * @param dto 云平台DTO
     * @return 云平台配置
     */
    private CloudPlatformConfig convertToConfig(CloudPlatformDTO dto) {
        CloudPlatformConfig config = new CloudPlatformConfig();
        config.setId(dto.getId());
        config.setName(dto.getName());
        config.setType(dto.getType());
        config.setUrl(dto.getUrl());
        config.setSecret(dto.getSecret());
        config.setRegion(dto.getRegion());
        config.setVersion(dto.getVersion());
        config.setTags(dto.getTags());
        // 注意：这里需要从认证上下文获取用户ID，暂时设为null
        // 在实际使用时，调用方应该提供正确的用户ID
        config.setUserId(null);
        return config;
    }

    /**
     * 使用云平台ID和用户ID创建配置
     * 这个方法允许调用方提供用户ID
     *
     * @param platformId 云平台ID
     * @param userId 用户ID
     * @return 云平台配置
     */
    public CloudPlatformConfig createConfig(Long platformId, Long userId) {
        return new CloudPlatformConfig(platformId, userId);
    }

    /**
     * 使用完整配置信息创建配置
     *
     * @param name 平台名称
     * @param type 平台类型
     * @param url 访问地址
     * @param secret 认证密钥
     * @param userId 用户ID
     * @return 云平台配置
     */
    public CloudPlatformConfig createConfig(String name, String type, String url, String secret, Long userId) {
        return new CloudPlatformConfig(name, type, url, secret, userId);
    }

    /**
     * 使用配置对象执行 Apply 操作
     * 提供给需要精确控制的场景
     *
     * @param request Apply 请求
     * @return 操作结果
     */
    public Mono<K8sOperationResult> apply(K8sApplyRequest request) {
        return operationService.apply(request);
    }

    /**
     * 使用配置对象执行 Delete 操作
     * 提供给需要精确控制的场景
     *
     * @param request Delete 请求
     * @return 操作结果
     */
    public Mono<K8sOperationResult> delete(K8sDeleteRequest request) {
        return operationService.delete(request);
    }
}
