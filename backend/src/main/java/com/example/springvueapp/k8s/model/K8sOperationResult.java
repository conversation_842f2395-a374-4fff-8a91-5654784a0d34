package com.example.springvueapp.k8s.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Kubernetes 操作结果模型
 * 用于返回 K8s 操作的执行结果
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class K8sOperationResult {

    /**
     * 操作是否成功
     */
    private Boolean success;

    /**
     * 操作类型（apply, delete）
     */
    private String operation;

    /**
     * 操作消息
     */
    private String message;

    /**
     * 处理的资源列表
     */
    private List<K8sResourceInfo> resources;

    /**
     * 操作开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 操作结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 操作耗时（毫秒）
     */
    private Long duration;

    /**
     * 错误信息（如果操作失败）
     */
    private String errorMessage;

    /**
     * 错误详情
     */
    private String errorDetails;

    /**
     * 额外的操作信息
     */
    private Map<String, Object> metadata;

    // 构造函数
    public K8sOperationResult() {
    }

    public K8sOperationResult(Boolean success, String operation, String message) {
        this.success = success;
        this.operation = operation;
        this.message = message;
        this.endTime = LocalDateTime.now();
    }

    // 静态工厂方法
    public static K8sOperationResult success(String operation, String message, List<K8sResourceInfo> resources) {
        K8sOperationResult result = new K8sOperationResult(true, operation, message);
        result.setResources(resources);
        return result;
    }

    public static K8sOperationResult failure(String operation, String errorMessage, String errorDetails) {
        K8sOperationResult result = new K8sOperationResult(false, operation, "操作失败");
        result.setErrorMessage(errorMessage);
        result.setErrorDetails(errorDetails);
        return result;
    }

    // Getter 和 Setter 方法
    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<K8sResourceInfo> getResources() {
        return resources;
    }

    public void setResources(List<K8sResourceInfo> resources) {
        this.resources = resources;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getErrorDetails() {
        return errorDetails;
    }

    public void setErrorDetails(String errorDetails) {
        this.errorDetails = errorDetails;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    /**
     * 计算并设置操作耗时
     */
    public void calculateDuration() {
        if (startTime != null && endTime != null) {
            this.duration = java.time.Duration.between(startTime, endTime).toMillis();
        }
    }

    @Override
    public String toString() {
        return "K8sOperationResult{" +
                "success=" + success +
                ", operation='" + operation + '\'' +
                ", message='" + message + '\'' +
                ", resourceCount=" + (resources != null ? resources.size() : 0) +
                ", duration=" + duration +
                ", errorMessage='" + errorMessage + '\'' +
                '}';
    }
}
