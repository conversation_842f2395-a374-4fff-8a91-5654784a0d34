package com.example.springvueapp.k8s.service;

import com.example.springvueapp.k8s.exception.K8sException;
import io.fabric8.kubernetes.api.model.HasMetadata;
import io.fabric8.kubernetes.client.utils.Serialization;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * Kubernetes YAML 解析器
 * 负责解析包含多个资源的 YAML 文档
 */
@Component
public class K8sYamlParser {

    private static final Logger logger = LoggerFactory.getLogger(K8sYamlParser.class);

    /**
     * YAML 文档分隔符
     */
    private static final String YAML_SEPARATOR = "---";

    /**
     * 解析 YAML 内容为 Kubernetes 资源列表
     * 支持单个资源和多资源文档（使用 --- 分隔）
     *
     * @param yamlContent YAML 内容
     * @return Kubernetes 资源列表
     */
    public List<HasMetadata> parseYaml(String yamlContent) {
        if (yamlContent == null || yamlContent.trim().isEmpty()) {
            throw K8sException.yamlError("YAML内容不能为空");
        }

        try {
            List<HasMetadata> resources = new ArrayList<>();
            
            // 分割多个YAML文档
            String[] yamlDocuments = splitYamlDocuments(yamlContent);
            
            for (String yamlDoc : yamlDocuments) {
                if (yamlDoc.trim().isEmpty()) {
                    continue;
                }
                
                // 解析单个YAML文档
                HasMetadata resource = parseYamlDocument(yamlDoc);
                if (resource != null) {
                    resources.add(resource);
                    logger.debug("解析到K8s资源: {} {}/{}", 
                        resource.getKind(), 
                        resource.getMetadata().getNamespace(), 
                        resource.getMetadata().getName());
                }
            }
            
            if (resources.isEmpty()) {
                throw K8sException.yamlError("YAML内容中未找到有效的Kubernetes资源");
            }
            
            logger.info("成功解析YAML，共找到 {} 个Kubernetes资源", resources.size());
            return resources;
            
        } catch (Exception e) {
            if (e instanceof K8sException) {
                throw e;
            }
            throw K8sException.yamlError("解析YAML内容失败: " + e.getMessage(), e);
        }
    }

    /**
     * 分割多个 YAML 文档
     *
     * @param yamlContent 完整的YAML内容
     * @return YAML文档数组
     */
    private String[] splitYamlDocuments(String yamlContent) {
        // 使用正则表达式分割，确保 --- 在行首
        String[] documents = yamlContent.split("(?m)^---\\s*$");
        
        // 过滤空文档
        List<String> validDocuments = new ArrayList<>();
        for (String doc : documents) {
            String trimmedDoc = doc.trim();
            if (!trimmedDoc.isEmpty()) {
                validDocuments.add(trimmedDoc);
            }
        }
        
        return validDocuments.toArray(new String[0]);
    }

    /**
     * 解析单个 YAML 文档
     *
     * @param yamlDocument 单个YAML文档内容
     * @return Kubernetes 资源对象
     */
    private HasMetadata parseYamlDocument(String yamlDocument) {
        try {
            // 使用 Fabric8 的序列化工具解析YAML
            InputStream inputStream = new ByteArrayInputStream(yamlDocument.getBytes());
            Object resource = Serialization.unmarshal(inputStream);
            
            if (resource instanceof HasMetadata) {
                HasMetadata k8sResource = (HasMetadata) resource;
                
                // 验证资源的基本信息
                validateResource(k8sResource);
                
                return k8sResource;
            } else {
                logger.warn("解析的对象不是有效的Kubernetes资源: {}", resource.getClass().getName());
                return null;
            }
            
        } catch (Exception e) {
            throw K8sException.yamlError("解析YAML文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证 Kubernetes 资源的基本信息
     *
     * @param resource Kubernetes 资源
     */
    private void validateResource(HasMetadata resource) {
        if (resource.getKind() == null || resource.getKind().trim().isEmpty()) {
            throw K8sException.yamlError("Kubernetes资源缺少kind字段");
        }
        
        if (resource.getApiVersion() == null || resource.getApiVersion().trim().isEmpty()) {
            throw K8sException.yamlError("Kubernetes资源缺少apiVersion字段");
        }
        
        if (resource.getMetadata() == null) {
            throw K8sException.yamlError("Kubernetes资源缺少metadata字段");
        }
        
        if (resource.getMetadata().getName() == null || resource.getMetadata().getName().trim().isEmpty()) {
            throw K8sException.yamlError("Kubernetes资源缺少metadata.name字段");
        }
    }

    /**
     * 验证 YAML 内容格式
     * 不进行完整解析，只检查基本格式
     *
     * @param yamlContent YAML 内容
     * @return 是否为有效格式
     */
    public boolean validateYamlFormat(String yamlContent) {
        if (yamlContent == null || yamlContent.trim().isEmpty()) {
            return false;
        }

        try {
            // 尝试解析YAML
            parseYaml(yamlContent);
            return true;
        } catch (Exception e) {
            logger.debug("YAML格式验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取 YAML 内容中的资源数量
     *
     * @param yamlContent YAML 内容
     * @return 资源数量
     */
    public int getResourceCount(String yamlContent) {
        try {
            List<HasMetadata> resources = parseYaml(yamlContent);
            return resources.size();
        } catch (Exception e) {
            logger.debug("获取资源数量失败: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 提取 YAML 内容中的资源信息摘要
     *
     * @param yamlContent YAML 内容
     * @return 资源信息摘要列表
     */
    public List<String> getResourceSummary(String yamlContent) {
        List<String> summary = new ArrayList<>();
        
        try {
            List<HasMetadata> resources = parseYaml(yamlContent);
            
            for (HasMetadata resource : resources) {
                String resourceInfo = String.format("%s/%s", 
                    resource.getKind(), 
                    resource.getMetadata().getName());
                
                if (resource.getMetadata().getNamespace() != null) {
                    resourceInfo += " (namespace: " + resource.getMetadata().getNamespace() + ")";
                }
                
                summary.add(resourceInfo);
            }
            
        } catch (Exception e) {
            logger.debug("获取资源摘要失败: {}", e.getMessage());
            summary.add("解析失败: " + e.getMessage());
        }
        
        return summary;
    }
}
